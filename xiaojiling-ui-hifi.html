<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>小机灵 - 荷兰华人数字生活平台 高保真UI设计</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Noto+Sans+SC:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', 'Noto Sans SC', sans-serif;
            background: #1a1a1a;
            color: white;
            padding: 40px;
            min-height: 100vh;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
        }

        .title {
            text-align: center;
            font-size: 32px;
            font-weight: 700;
            margin-bottom: 40px;
            background: linear-gradient(135deg, #00D4AA 0%, #00A8FF 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .screens-grid {
            display: grid;
            grid-template-columns: repeat(5, 1fr);
            gap: 20px;
            margin-bottom: 40px;
        }

        .phone-frame {
            width: 200px;
            height: 400px;
            background: #2d2d2d;
            border-radius: 20px;
            padding: 8px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.3);
            transition: transform 0.3s ease;
        }

        .phone-frame:hover {
            transform: translateY(-5px);
        }

        .screen {
            width: 100%;
            height: 100%;
            background: #fff;
            border-radius: 16px;
            overflow: hidden;
            position: relative;
        }

        .status-bar {
            height: 20px;
            background: #000;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 12px;
            font-size: 8px;
            color: white;
            font-weight: 500;
        }

        .screen-content {
            height: calc(100% - 20px);
            overflow: hidden;
            position: relative;
        }

        /* Welcome Screen */
        .welcome-screen {
            background: linear-gradient(135deg, #00D4AA 0%, #00A8FF 100%);
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            text-align: center;
            color: white;
        }

        .logo {
            font-size: 24px;
            font-weight: 700;
            margin-bottom: 8px;
        }

        .subtitle {
            font-size: 10px;
            margin-bottom: 30px;
            opacity: 0.9;
        }

        .lang-options {
            display: flex;
            gap: 8px;
        }

        .lang-btn {
            padding: 6px 12px;
            background: rgba(255,255,255,0.2);
            border: 1px solid rgba(255,255,255,0.3);
            border-radius: 12px;
            color: white;
            font-size: 8px;
            font-weight: 500;
        }

        .lang-btn.active {
            background: rgba(255,255,255,0.3);
        }

        /* Login Screen */
        .login-screen {
            background: #f8f9fa;
            padding: 20px;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }

        .login-title {
            font-size: 18px;
            font-weight: 600;
            color: #333;
            text-align: center;
            margin-bottom: 20px;
        }

        .input-field {
            width: 100%;
            padding: 12px;
            border: 1px solid #e1e5e9;
            border-radius: 8px;
            font-size: 10px;
            margin-bottom: 12px;
            background: white;
        }

        .login-btn {
            width: 100%;
            padding: 12px;
            background: linear-gradient(135deg, #00D4AA 0%, #00A8FF 100%);
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 10px;
            font-weight: 600;
            margin: 16px 0;
        }

        .social-login {
            display: flex;
            gap: 8px;
            justify-content: center;
        }

        .social-btn {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            border: none;
            font-size: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        /* Home Screen */
        .home-screen {
            background: #f8f9fa;
        }

        .header {
            background: white;
            padding: 12px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.05);
        }

        .search-bar {
            display: flex;
            align-items: center;
            background: #f5f5f5;
            border-radius: 20px;
            padding: 8px 12px;
            margin-bottom: 8px;
        }

        .search-input {
            border: none;
            background: none;
            flex: 1;
            margin-left: 6px;
            font-size: 8px;
            color: #666;
        }

        .city-selector {
            font-size: 8px;
            color: #666;
            text-align: center;
        }

        .banner {
            background: linear-gradient(135deg, #00D4AA 0%, #00A8FF 100%);
            color: white;
            padding: 16px;
            margin: 12px;
            border-radius: 12px;
            text-align: center;
            font-size: 10px;
            font-weight: 600;
        }

        .quick-nav {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 12px;
            padding: 12px;
        }

        .nav-item {
            background: white;
            padding: 12px;
            border-radius: 12px;
            text-align: center;
            box-shadow: 0 2px 8px rgba(0,0,0,0.05);
        }

        .nav-icon {
            font-size: 16px;
            margin-bottom: 4px;
        }

        .nav-label {
            font-size: 8px;
            color: #333;
            font-weight: 500;
        }

        .bottom-tab {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 50px;
            background: white;
            display: flex;
            justify-content: space-around;
            align-items: center;
            border-top: 1px solid #f0f0f0;
        }

        .tab-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            font-size: 6px;
            color: #999;
        }

        .tab-item.active {
            color: #00D4AA;
        }

        .tab-icon {
            font-size: 12px;
            margin-bottom: 2px;
        }

        /* Content Pages */
        .content-page {
            background: #f8f9fa;
            padding: 12px;
        }

        .page-title {
            font-size: 14px;
            font-weight: 600;
            color: #333;
            margin-bottom: 12px;
        }

        .feature-card {
            background: white;
            border-radius: 12px;
            padding: 12px;
            margin-bottom: 12px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.05);
        }

        .card-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
        }

        .card-title {
            font-size: 10px;
            font-weight: 600;
            color: #333;
        }

        .card-badge {
            background: #00D4AA;
            color: white;
            padding: 2px 6px;
            border-radius: 4px;
            font-size: 6px;
            font-weight: 500;
        }

        .card-meta {
            font-size: 8px;
            color: #666;
        }

        .category-tabs {
            display: flex;
            gap: 6px;
            margin-bottom: 12px;
        }

        .tab {
            padding: 4px 8px;
            background: white;
            border-radius: 12px;
            font-size: 7px;
            color: #666;
            border: 1px solid #e0e0e0;
        }

        .tab.active {
            background: #00D4AA;
            color: white;
            border-color: #00D4AA;
        }

        /* Profile Screen */
        .profile-screen {
            background: linear-gradient(135deg, #00D4AA 0%, #00A8FF 100%);
        }

        .profile-header {
            padding: 20px;
            text-align: center;
            color: white;
        }

        .avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: rgba(255,255,255,0.2);
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 8px;
            font-size: 16px;
        }

        .username {
            font-size: 12px;
            font-weight: 600;
            margin-bottom: 8px;
        }

        .user-stats {
            display: flex;
            justify-content: center;
            gap: 12px;
            font-size: 7px;
        }

        .menu-list {
            background: white;
            border-radius: 16px 16px 0 0;
            padding: 16px;
            margin-top: 12px;
            height: calc(100% - 120px);
        }

        .menu-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid #f0f0f0;
            font-size: 8px;
            color: #333;
        }

        .menu-item:last-child {
            border-bottom: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="title">小机灵 - 荷兰华人数字生活平台</h1>
        
        <div class="screens-grid">
            <!-- Row 1: Core Screens -->
            <!-- 1. Welcome Screen -->
            <div class="phone-frame">
                <div class="screen">
                    <div class="status-bar">
                        <span>9:41</span>
                        <span>🔋100%</span>
                    </div>
                    <div class="screen-content welcome-screen">
                        <div class="logo">小机灵</div>
                        <div class="subtitle">荷兰华人数字生活平台</div>
                        <div class="lang-options">
                            <div class="lang-btn active">中文</div>
                            <div class="lang-btn">EN</div>
                            <div class="lang-btn">NL</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 2. Login Screen -->
            <div class="phone-frame">
                <div class="screen">
                    <div class="status-bar">
                        <span>9:41</span>
                        <span>🔋100%</span>
                    </div>
                    <div class="screen-content login-screen">
                        <div class="login-title">欢迎使用小机灵 👋</div>
                        <input type="text" class="input-field" placeholder="📧 邮箱或手机号">
                        <input type="password" class="input-field" placeholder="🔒 密码">
                        <button class="login-btn">登录</button>
                        <div class="social-login">
                            <button class="social-btn" style="background: #1AAD19; color: white;">微</button>
                            <button class="social-btn" style="background: #4285F4; color: white;">G</button>
                            <button class="social-btn" style="background: #000; color: white;">🍎</button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 3. Home Screen -->
            <div class="phone-frame">
                <div class="screen">
                    <div class="status-bar">
                        <span>9:41</span>
                        <span>🔋100%</span>
                    </div>
                    <div class="screen-content home-screen">
                        <div class="header">
                            <div class="search-bar">
                                <span>🔍</span>
                                <input type="text" class="search-input" placeholder="搜索餐厅、工作、房源...">
                            </div>
                            <div class="city-selector">📍 阿姆斯特丹</div>
                        </div>
                        <div class="banner">
                            🎉 新用户专享福利 | AI助手免费体验
                        </div>
                        <div class="quick-nav">
                            <div class="nav-item">
                                <div class="nav-icon">🍜</div>
                                <div class="nav-label">美食</div>
                            </div>
                            <div class="nav-item">
                                <div class="nav-icon">🏠</div>
                                <div class="nav-label">租房</div>
                            </div>
                            <div class="nav-item">
                                <div class="nav-icon">💼</div>
                                <div class="nav-label">求职</div>
                            </div>
                            <div class="nav-item">
                                <div class="nav-icon">✈️</div>
                                <div class="nav-label">旅行</div>
                            </div>
                            <div class="nav-item">
                                <div class="nav-icon">💰</div>
                                <div class="nav-label">优惠</div>
                            </div>
                            <div class="nav-item">
                                <div class="nav-icon">🤖</div>
                                <div class="nav-label">AI助手</div>
                            </div>
                        </div>
                        <div class="bottom-tab">
                            <div class="tab-item active">
                                <div class="tab-icon">🏠</div>
                                <div>首页</div>
                            </div>
                            <div class="tab-item">
                                <div class="tab-icon">🔍</div>
                                <div>发现</div>
                            </div>
                            <div class="tab-item">
                                <div class="tab-icon">💬</div>
                                <div>消息</div>
                            </div>
                            <div class="tab-item">
                                <div class="tab-icon">👤</div>
                                <div>我的</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 4. Restaurant Page -->
            <div class="phone-frame">
                <div class="screen">
                    <div class="status-bar">
                        <span>9:41</span>
                        <span>🔋100%</span>
                    </div>
                    <div class="screen-content content-page">
                        <div class="page-title">🍜 美食推荐</div>
                        <div class="category-tabs">
                            <div class="tab active">中餐</div>
                            <div class="tab">西餐</div>
                            <div class="tab">日韩</div>
                            <div class="tab">外卖</div>
                        </div>
                        <div class="feature-card">
                            <div class="card-header">
                                <div class="card-title">海底捞火锅</div>
                                <div class="card-badge">热门</div>
                            </div>
                            <div class="card-meta">⭐ 4.8 | 📍 市中心 | 💰 €25/人</div>
                        </div>
                        <div class="feature-card">
                            <div class="card-header">
                                <div class="card-title">兰州拉面</div>
                                <div class="card-badge">新店</div>
                            </div>
                            <div class="card-meta">⭐ 4.6 | 📍 唐人街 | 💰 €12/人</div>
                        </div>
                        <div class="feature-card">
                            <div class="card-header">
                                <div class="card-title">🤖 AI美食助手</div>
                                <div class="card-badge">智能</div>
                            </div>
                            <div class="card-meta">根据口味偏好推荐餐厅</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 5. Housing Page -->
            <div class="phone-frame">
                <div class="screen">
                    <div class="status-bar">
                        <span>9:41</span>
                        <span>🔋100%</span>
                    </div>
                    <div class="screen-content content-page">
                        <div class="page-title">🏠 租房信息</div>
                        <div class="category-tabs">
                            <div class="tab active">整租</div>
                            <div class="tab">合租</div>
                            <div class="tab">学生房</div>
                            <div class="tab">短租</div>
                        </div>
                        <div class="feature-card">
                            <div class="card-header">
                                <div class="card-title">市中心2室1厅</div>
                                <div class="card-badge">新房源</div>
                            </div>
                            <div class="card-meta">💰 €1800/月 | 📍 阿姆斯特丹中心</div>
                        </div>
                        <div class="feature-card">
                            <div class="card-header">
                                <div class="card-title">学生公寓单间</div>
                                <div class="card-badge">包水电</div>
                            </div>
                            <div class="card-meta">💰 €650/月 | 📍 乌特勒支大学区</div>
                        </div>
                        <div class="feature-card">
                            <div class="card-header">
                                <div class="card-title">🤖 AI找房助手</div>
                                <div class="card-badge">智能</div>
                            </div>
                            <div class="card-meta">智能匹配理想房源</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="screens-grid">
            <!-- Row 2: Feature Screens -->
            <!-- 6. Jobs Page -->
            <div class="phone-frame">
                <div class="screen">
                    <div class="status-bar">
                        <span>9:41</span>
                        <span>🔋100%</span>
                    </div>
                    <div class="screen-content content-page">
                        <div class="page-title">💼 求职招聘</div>
                        <div class="category-tabs">
                            <div class="tab active">兼职</div>
                            <div class="tab">全职</div>
                            <div class="tab">实习</div>
                            <div class="tab">远程</div>
                        </div>
                        <div class="feature-card">
                            <div class="card-header">
                                <div class="card-title">中文客服</div>
                                <div class="card-badge">急招</div>
                            </div>
                            <div class="card-meta">💰 €15/小时 | 📍 阿姆斯特丹</div>
                        </div>
                        <div class="feature-card">
                            <div class="card-header">
                                <div class="card-title">软件工程师</div>
                                <div class="card-badge">高薪</div>
                            </div>
                            <div class="card-meta">💰 €4500/月 | 📍 海牙</div>
                        </div>
                        <div class="feature-card">
                            <div class="card-header">
                                <div class="card-title">🤖 AI简历优化</div>
                                <div class="card-badge">免费</div>
                            </div>
                            <div class="card-meta">AI帮你优化简历内容</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 7. Travel Page -->
            <div class="phone-frame">
                <div class="screen">
                    <div class="status-bar">
                        <span>9:41</span>
                        <span>🔋100%</span>
                    </div>
                    <div class="screen-content content-page">
                        <div class="page-title">✈️ 旅行规划</div>
                        <div class="category-tabs">
                            <div class="tab active">欧洲游</div>
                            <div class="tab">周边游</div>
                            <div class="tab">一日游</div>
                            <div class="tab">攻略</div>
                        </div>
                        <div class="feature-card">
                            <div class="card-header">
                                <div class="card-title">巴黎3日游</div>
                                <div class="card-badge">热门</div>
                            </div>
                            <div class="card-meta">🚄 高铁直达 | 💰 €299起</div>
                        </div>
                        <div class="feature-card">
                            <div class="card-header">
                                <div class="card-title">羊角村一日游</div>
                                <div class="card-badge">推荐</div>
                            </div>
                            <div class="card-meta">🚌 大巴接送 | 💰 €45/人</div>
                        </div>
                        <div class="feature-card">
                            <div class="card-header">
                                <div class="card-title">🤖 AI行程规划</div>
                                <div class="card-badge">智能</div>
                            </div>
                            <div class="card-meta">个性化定制旅行路线</div>
                        </div>
                    </div>
                </div>
            </div>
