<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>小机灵 - 荷兰华人数字生活平台 高保真UI设计</title>
    <link href="https://fonts.googleapis.com/css2?family=Nunito:wght@300;400;500;600;700;800&family=Noto+Sans+SC:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Noto Sans SC', 'Nunito', sans-serif;
            background: linear-gradient(135deg, #f8f6f0 0%, #e8f4f8 100%);
            color: #2c3e50;
            padding: 40px;
            min-height: 100vh;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
        }

        .title {
            text-align: center;
            font-size: 36px;
            font-weight: 800;
            margin-bottom: 20px;
            background: linear-gradient(135deg, #FF8A65 0%, #FFB74D 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .subtitle {
            text-align: center;
            font-size: 16px;
            color: #7f8c8d;
            margin-bottom: 40px;
            font-style: italic;
        }

        .screens-grid {
            display: grid;
            grid-template-columns: repeat(5, 1fr);
            gap: 24px;
            margin-bottom: 40px;
        }

        .phone-frame {
            width: 220px;
            height: 440px;
            background: linear-gradient(145deg, #ffffff 0%, #f5f5f5 100%);
            border-radius: 24px;
            padding: 10px;
            box-shadow: 0 12px 40px rgba(255,138,101,0.15), 0 4px 16px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            border: 2px solid rgba(255,183,77,0.1);
        }

        .phone-frame:hover {
            transform: translateY(-8px) scale(1.02);
            box-shadow: 0 20px 60px rgba(255,138,101,0.25), 0 8px 24px rgba(0,0,0,0.15);
        }

        .screen {
            width: 100%;
            height: 100%;
            background: #fff;
            border-radius: 20px;
            overflow: hidden;
            position: relative;
            border: 1px solid rgba(255,183,77,0.1);
        }

        .status-bar {
            height: 24px;
            background: linear-gradient(135deg, #FF8A65 0%, #FFB74D 100%);
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 14px;
            font-size: 9px;
            color: white;
            font-weight: 600;
        }

        .screen-content {
            height: calc(100% - 20px);
            overflow: hidden;
            position: relative;
        }

        /* Welcome Screen */
        .welcome-screen {
            background: linear-gradient(135deg, #FF8A65 0%, #FFB74D 100%);
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            text-align: center;
            color: white;
            position: relative;
            overflow: hidden;
        }

        .welcome-screen::before {
            content: '🦊';
            position: absolute;
            top: 60px;
            right: 20px;
            font-size: 40px;
            opacity: 0.3;
            animation: float 3s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }

        .logo {
            font-size: 28px;
            font-weight: 800;
            margin-bottom: 8px;
            text-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .welcome-subtitle {
            font-size: 11px;
            margin-bottom: 30px;
            opacity: 0.95;
            font-weight: 500;
        }

        .lang-options {
            display: flex;
            gap: 10px;
        }

        .lang-btn {
            padding: 8px 14px;
            background: rgba(255,255,255,0.25);
            border: 1.5px solid rgba(255,255,255,0.4);
            border-radius: 16px;
            color: white;
            font-size: 9px;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .lang-btn.active {
            background: rgba(255,255,255,0.4);
            transform: scale(1.05);
        }

        /* Login Screen */
        .login-screen {
            background: linear-gradient(135deg, #fef9f3 0%, #f0f8ff 100%);
            padding: 24px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            position: relative;
        }

        .login-screen::before {
            content: '🌸';
            position: absolute;
            top: 30px;
            left: 30px;
            font-size: 24px;
            opacity: 0.4;
        }

        .login-title {
            font-size: 20px;
            font-weight: 700;
            color: #2c3e50;
            text-align: center;
            margin-bottom: 24px;
        }

        .input-field {
            width: 100%;
            padding: 14px 16px;
            border: 2px solid #e8f4f8;
            border-radius: 12px;
            font-size: 11px;
            margin-bottom: 14px;
            background: white;
            transition: border-color 0.3s ease;
            font-family: 'Noto Sans SC', sans-serif;
        }

        .input-field:focus {
            outline: none;
            border-color: #FFB74D;
        }

        .login-btn {
            width: 100%;
            padding: 14px;
            background: linear-gradient(135deg, #FF8A65 0%, #FFB74D 100%);
            color: white;
            border: none;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 700;
            margin: 18px 0;
            box-shadow: 0 4px 12px rgba(255,138,101,0.3);
            transition: transform 0.2s ease;
        }

        .login-btn:hover {
            transform: translateY(-2px);
        }

        .social-login {
            display: flex;
            gap: 12px;
            justify-content: center;
        }

        .social-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            border: none;
            font-size: 14px;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: transform 0.2s ease;
        }

        .social-btn:hover {
            transform: scale(1.1);
        }

        /* Home Screen */
        .home-screen {
            background: linear-gradient(135deg, #fef9f3 0%, #f0f8ff 100%);
        }

        .header {
            background: white;
            padding: 16px;
            box-shadow: 0 2px 12px rgba(255,138,101,0.1);
            border-bottom: 1px solid rgba(255,183,77,0.1);
        }

        .search-bar {
            display: flex;
            align-items: center;
            background: #f8f6f0;
            border-radius: 24px;
            padding: 10px 16px;
            margin-bottom: 10px;
            border: 2px solid rgba(255,183,77,0.1);
        }

        .search-input {
            border: none;
            background: none;
            flex: 1;
            margin-left: 8px;
            font-size: 10px;
            color: #2c3e50;
            font-family: 'Noto Sans SC', sans-serif;
        }

        .search-input::placeholder {
            color: #95a5a6;
        }

        .city-selector {
            font-size: 9px;
            color: #7f8c8d;
            text-align: center;
            font-weight: 500;
        }

        .banner {
            background: linear-gradient(135deg, #FF8A65 0%, #FFB74D 100%);
            color: white;
            padding: 18px;
            margin: 16px;
            border-radius: 16px;
            text-align: center;
            font-size: 11px;
            font-weight: 600;
            box-shadow: 0 4px 16px rgba(255,138,101,0.2);
            position: relative;
            overflow: hidden;
        }

        .banner::before {
            content: '✨';
            position: absolute;
            top: 8px;
            right: 16px;
            font-size: 16px;
            opacity: 0.7;
        }

        .quick-nav {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 14px;
            padding: 16px;
        }

        .nav-item {
            background: white;
            padding: 16px 12px;
            border-radius: 16px;
            text-align: center;
            box-shadow: 0 3px 12px rgba(255,138,101,0.1);
            border: 1px solid rgba(255,183,77,0.1);
            transition: all 0.3s ease;
        }

        .nav-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(255,138,101,0.15);
        }

        .nav-icon {
            font-size: 20px;
            margin-bottom: 6px;
            display: block;
        }

        .nav-label {
            font-size: 9px;
            color: #2c3e50;
            font-weight: 600;
        }

        .bottom-tab {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 56px;
            background: white;
            display: flex;
            justify-content: space-around;
            align-items: center;
            border-top: 2px solid #f8f6f0;
            box-shadow: 0 -2px 12px rgba(255,138,101,0.1);
        }

        .tab-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            font-size: 7px;
            color: #95a5a6;
            transition: all 0.3s ease;
            padding: 4px;
            border-radius: 8px;
        }

        .tab-item.active {
            color: #FF8A65;
            background: rgba(255,138,101,0.1);
        }

        .tab-icon {
            font-size: 14px;
            margin-bottom: 3px;
        }

        /* Content Pages */
        .content-page {
            background: linear-gradient(135deg, #fef9f3 0%, #f0f8ff 100%);
            padding: 16px;
        }

        .page-title {
            font-size: 16px;
            font-weight: 700;
            color: #2c3e50;
            margin-bottom: 16px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .feature-card {
            background: white;
            border-radius: 16px;
            padding: 16px;
            margin-bottom: 14px;
            box-shadow: 0 3px 12px rgba(255,138,101,0.1);
            border: 1px solid rgba(255,183,77,0.1);
            transition: transform 0.2s ease;
        }

        .feature-card:hover {
            transform: translateY(-2px);
        }

        .card-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }

        .card-title {
            font-size: 11px;
            font-weight: 600;
            color: #2c3e50;
        }

        .card-badge {
            background: linear-gradient(135deg, #FF8A65 0%, #FFB74D 100%);
            color: white;
            padding: 3px 8px;
            border-radius: 8px;
            font-size: 7px;
            font-weight: 600;
            box-shadow: 0 2px 4px rgba(255,138,101,0.2);
        }

        .card-meta {
            font-size: 9px;
            color: #7f8c8d;
            line-height: 1.4;
        }

        .category-tabs {
            display: flex;
            gap: 8px;
            margin-bottom: 16px;
            overflow-x: auto;
            padding-bottom: 4px;
        }

        .tab {
            padding: 6px 12px;
            background: white;
            border-radius: 16px;
            font-size: 8px;
            color: #7f8c8d;
            border: 2px solid #e8f4f8;
            white-space: nowrap;
            transition: all 0.3s ease;
            font-weight: 500;
        }

        .tab.active {
            background: linear-gradient(135deg, #FF8A65 0%, #FFB74D 100%);
            color: white;
            border-color: #FF8A65;
            transform: scale(1.05);
        }

        /* Profile Screen */
        .profile-screen {
            background: linear-gradient(135deg, #FF8A65 0%, #FFB74D 100%);
            position: relative;
            overflow: hidden;
        }

        .profile-screen::before {
            content: '🌟';
            position: absolute;
            top: 20px;
            right: 20px;
            font-size: 24px;
            opacity: 0.3;
        }

        .profile-header {
            padding: 24px;
            text-align: center;
            color: white;
        }

        .avatar {
            width: 48px;
            height: 48px;
            border-radius: 50%;
            background: rgba(255,255,255,0.25);
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 10px;
            font-size: 20px;
            border: 2px solid rgba(255,255,255,0.3);
        }

        .username {
            font-size: 14px;
            font-weight: 700;
            margin-bottom: 10px;
            text-shadow: 0 1px 2px rgba(0,0,0,0.1);
        }

        .user-stats {
            display: flex;
            justify-content: center;
            gap: 16px;
            font-size: 8px;
            font-weight: 500;
        }

        .menu-list {
            background: white;
            border-radius: 20px 20px 0 0;
            padding: 20px;
            margin-top: 16px;
            height: calc(100% - 140px);
        }

        .menu-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 0;
            border-bottom: 1px solid #f8f6f0;
            font-size: 9px;
            color: #2c3e50;
            font-weight: 500;
        }

        .menu-item:last-child {
            border-bottom: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="title">小机灵 - 荷兰华人数字生活平台</h1>
        <p class="subtitle">"生活是江湖，机灵来破局" - 让每一个远在欧洲的华人，都有一个懂你的数字搭子</p>

        <div class="screens-grid">
            <!-- Row 1: Core Screens -->
            <!-- 1. Welcome Screen -->
            <div class="phone-frame">
                <div class="screen">
                    <div class="status-bar">
                        <span>9:41</span>
                        <span>🔋100%</span>
                    </div>
                    <div class="screen-content welcome-screen">
                        <div class="logo">小机灵</div>
                        <div class="welcome-subtitle">荷兰华人数字生活平台</div>
                        <div class="lang-options">
                            <div class="lang-btn active">中文</div>
                            <div class="lang-btn">EN</div>
                            <div class="lang-btn">NL</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 2. Login Screen -->
            <div class="phone-frame">
                <div class="screen">
                    <div class="status-bar">
                        <span>9:41</span>
                        <span>🔋100%</span>
                    </div>
                    <div class="screen-content login-screen">
                        <div class="login-title">欢迎使用小机灵 👋</div>
                        <input type="text" class="input-field" placeholder="📧 邮箱或手机号">
                        <input type="password" class="input-field" placeholder="🔒 密码">
                        <button class="login-btn">登录</button>
                        <div class="social-login">
                            <button class="social-btn" style="background: #1AAD19; color: white;">微</button>
                            <button class="social-btn" style="background: #4285F4; color: white;">G</button>
                            <button class="social-btn" style="background: #000; color: white;">🍎</button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 3. Home Screen -->
            <div class="phone-frame">
                <div class="screen">
                    <div class="status-bar">
                        <span>9:41</span>
                        <span>🔋100%</span>
                    </div>
                    <div class="screen-content home-screen">
                        <div class="header">
                            <div class="search-bar">
                                <span>🔍</span>
                                <input type="text" class="search-input" placeholder="搜索餐厅、工作、房源...">
                            </div>
                            <div class="city-selector">📍 阿姆斯特丹</div>
                        </div>
                        <div class="banner">
                            别看太多信息，跟着小机灵选就对了 🦊
                        </div>
                        <div class="quick-nav">
                            <div class="nav-item">
                                <div class="nav-icon">🍜</div>
                                <div class="nav-label">吃&喝</div>
                            </div>
                            <div class="nav-item">
                                <div class="nav-icon">🏠</div>
                                <div class="nav-label">住</div>
                            </div>
                            <div class="nav-item">
                                <div class="nav-icon">💼</div>
                                <div class="nav-label">工</div>
                            </div>
                            <div class="nav-item">
                                <div class="nav-icon">🎭</div>
                                <div class="nav-label">玩&乐</div>
                            </div>
                            <div class="nav-item">
                                <div class="nav-icon">💰</div>
                                <div class="nav-label">活</div>
                            </div>
                            <div class="nav-item">
                                <div class="nav-icon">🦊</div>
                                <div class="nav-label">小机灵</div>
                            </div>
                        </div>
                        <div class="bottom-tab">
                            <div class="tab-item active">
                                <div class="tab-icon">🏠</div>
                                <div>首页</div>
                            </div>
                            <div class="tab-item">
                                <div class="tab-icon">🔍</div>
                                <div>发现</div>
                            </div>
                            <div class="tab-item">
                                <div class="tab-icon">💬</div>
                                <div>消息</div>
                            </div>
                            <div class="tab-item">
                                <div class="tab-icon">👤</div>
                                <div>我的</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 4. 吃&喝 Page -->
            <div class="phone-frame">
                <div class="screen">
                    <div class="status-bar">
                        <span>9:41</span>
                        <span>🔋100%</span>
                    </div>
                    <div class="screen-content content-page">
                        <div class="page-title">🍜 这年头奶茶多，靠谱的不多</div>
                        <div class="category-tabs">
                            <div class="tab active">中餐</div>
                            <div class="tab">西餐</div>
                            <div class="tab">日韩</div>
                            <div class="tab">外卖</div>
                        </div>
                        <div class="feature-card">
                            <div class="card-header">
                                <div class="card-title">海底捞火锅</div>
                                <div class="card-badge">小机灵推荐</div>
                            </div>
                            <div class="card-meta">⭐ 4.8 | 📍 市中心 | 💰 €25/人<br>我看过你最近喜欢的地段，这家你应该会喜欢</div>
                        </div>
                        <div class="feature-card">
                            <div class="card-header">
                                <div class="card-title">兰州拉面</div>
                                <div class="card-badge">新店</div>
                            </div>
                            <div class="card-meta">⭐ 4.6 | 📍 唐人街 | 💰 €12/人<br>这家火锅排队很久，我给你找到了非高峰时段</div>
                        </div>
                        <div class="feature-card" style="background: linear-gradient(135deg, #fff8f0 0%, #f0f8ff 100%);">
                            <div class="card-header">
                                <div class="card-title">🦊 小机灵说</div>
                                <div class="card-badge">AI助手</div>
                            </div>
                            <div class="card-meta">"我今天看了100条餐厅评价，这3家值得你知道"</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 5. 住 Page -->
            <div class="phone-frame">
                <div class="screen">
                    <div class="status-bar">
                        <span>9:41</span>
                        <span>🔋100%</span>
                    </div>
                    <div class="screen-content content-page">
                        <div class="page-title">🏠 一千套房源，不如一个懂你的推荐</div>
                        <div class="category-tabs">
                            <div class="tab active">整租</div>
                            <div class="tab">合租</div>
                            <div class="tab">学生房</div>
                            <div class="tab">短租</div>
                        </div>
                        <div class="feature-card">
                            <div class="card-header">
                                <div class="card-title">市中心2室1厅</div>
                                <div class="card-badge">新房源</div>
                            </div>
                            <div class="card-meta">💰 €1800/月 | 📍 阿姆斯特丹中心<br>根据你的预算和地段偏好，这套最合适</div>
                        </div>
                        <div class="feature-card">
                            <div class="card-header">
                                <div class="card-title">学生公寓单间</div>
                                <div class="card-badge">包水电</div>
                            </div>
                            <div class="card-meta">💰 €650/月 | 📍 乌特勒支大学区<br>这房东照片P得比折扣还大，但房子确实不错</div>
                        </div>
                        <div class="feature-card" style="background: linear-gradient(135deg, #fff8f0 0%, #f0f8ff 100%);">
                            <div class="card-header">
                                <div class="card-title">🦊 AI找房助手</div>
                                <div class="card-badge">智能匹配</div>
                            </div>
                            <div class="card-meta">"我不是神，但我陪你闯日子，找房这事交给我"</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="screens-grid">
            <!-- Row 2: 核心功能页面 -->
            <!-- 6. 工 Page -->
            <div class="phone-frame">
                <div class="screen">
                    <div class="status-bar">
                        <span>9:41</span>
                        <span>🔋100%</span>
                    </div>
                    <div class="screen-content content-page">
                        <div class="page-title">💼 好岗位不等人，机灵都替你盯着呢</div>
                        <div class="category-tabs">
                            <div class="tab active">兼职</div>
                            <div class="tab">全职</div>
                            <div class="tab">实习</div>
                            <div class="tab">远程</div>
                        </div>
                        <div class="feature-card">
                            <div class="card-header">
                                <div class="card-title">中文客服</div>
                                <div class="card-badge">急招</div>
                            </div>
                            <div class="card-meta">💰 €15/小时 | 📍 阿姆斯特丹<br>AI帮你优化简历，匹配度95%</div>
                        </div>
                        <div class="feature-card">
                            <div class="card-header">
                                <div class="card-title">软件工程师</div>
                                <div class="card-badge">高薪</div>
                            </div>
                            <div class="card-meta">💰 €4500/月 | 📍 海牙<br>根据你的技能栈，这个岗位很适合</div>
                        </div>
                        <div class="feature-card" style="background: linear-gradient(135deg, #fff8f0 0%, #f0f8ff 100%);">
                            <div class="card-header">
                                <div class="card-title">🦊 AI简历助手</div>
                                <div class="card-badge">免费优化</div>
                            </div>
                            <div class="card-meta">"路太多，你选；事太杂，我来"</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 7. 玩&乐 Page -->
            <div class="phone-frame">
                <div class="screen">
                    <div class="status-bar">
                        <span>9:41</span>
                        <span>🔋100%</span>
                    </div>
                    <div class="screen-content content-page">
                        <div class="page-title">🎭 天气刚刚好，该出发了</div>
                        <div class="category-tabs">
                            <div class="tab active">欧洲游</div>
                            <div class="tab">周边游</div>
                            <div class="tab">一日游</div>
                            <div class="tab">攻略</div>
                        </div>
                        <div class="feature-card">
                            <div class="card-header">
                                <div class="card-title">巴黎3日游</div>
                                <div class="card-badge">热门</div>
                            </div>
                            <div class="card-meta">🚄 高铁直达 | 💰 €299起<br>个性化定制旅行路线，避开人群</div>
                        </div>
                        <div class="feature-card">
                            <div class="card-header">
                                <div class="card-title">羊角村一日游</div>
                                <div class="card-badge">推荐</div>
                            </div>
                            <div class="card-meta">🚌 大巴接送 | 💰 €45/人<br>宫崎骏同款风景，治愈系一日游</div>
                        </div>
                        <div class="feature-card" style="background: linear-gradient(135deg, #fff8f0 0%, #f0f8ff 100%);">
                            <div class="card-header">
                                <div class="card-title">🦊 AI行程规划</div>
                                <div class="card-badge">智能推荐</div>
                            </div>
                            <div class="card-meta">"在别处是路人，在我这儿你是主角"</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 8. 活 Page -->
            <div class="phone-frame">
                <div class="screen">
                    <div class="status-bar">
                        <span>9:41</span>
                        <span>🔋100%</span>
                    </div>
                    <div class="screen-content content-page">
                        <div class="page-title">💰 我命由我不由价，除了今天的五折</div>
                        <div class="category-tabs">
                            <div class="tab active">美食</div>
                            <div class="tab">购物</div>
                            <div class="tab">服务</div>
                            <div class="tab">海淘</div>
                        </div>
                        <div class="feature-card">
                            <div class="card-header">
                                <div class="card-title">麦当劳</div>
                                <div class="card-badge">7折</div>
                            </div>
                            <div class="card-meta">🍔 巨无霸套餐 | 💰 €6.29<br>价格战我不看，只看值不值</div>
                        </div>
                        <div class="feature-card">
                            <div class="card-header">
                                <div class="card-title">Albert Heijn</div>
                                <div class="card-badge">买2送1</div>
                            </div>
                            <div class="card-meta">🛒 亚洲食品专区优惠<br>你不想看的，我提前帮你屏蔽</div>
                        </div>
                        <div class="feature-card">
                            <div class="card-header">
                                <div class="card-title">天猫国际</div>
                                <div class="card-badge">返利5%</div>
                            </div>
                            <div class="card-meta">📦 海淘直邮荷兰<br>CPS联盟返利，省钱小能手</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 9. 小机灵AI助手 -->
            <div class="phone-frame">
                <div class="screen">
                    <div class="status-bar">
                        <span>9:41</span>
                        <span>🔋100%</span>
                    </div>
                    <div class="screen-content content-page">
                        <div class="page-title">🦊 小机灵不多，我是唯一</div>
                        <div class="feature-card" style="background: linear-gradient(135deg, #FF8A65 0%, #FFB74D 100%); color: white;">
                            <div class="card-header">
                                <div class="card-title" style="color: white;">🦊 小机灵AI助手</div>
                                <div class="card-badge" style="background: rgba(255,255,255,0.3);">在线</div>
                            </div>
                            <div class="card-meta" style="color: rgba(255,255,255,0.95);">"别急，我正在和生活死磕"</div>
                        </div>
                        <div class="feature-card">
                            <div class="card-header">
                                <div class="card-title">🏥 医疗保险咨询</div>
                                <div class="card-badge">热门</div>
                            </div>
                            <div class="card-meta">签证更新太复杂？我整理成图了，放心看不晕</div>
                        </div>
                        <div class="feature-card">
                            <div class="card-header">
                                <div class="card-title">🚗 驾照考试指南</div>
                                <div class="card-badge">实用</div>
                            </div>
                            <div class="card-meta">荷兰驾照考试全攻略，AI帮你划重点</div>
                        </div>
                        <div class="feature-card">
                            <div class="card-header">
                                <div class="card-title">🏦 银行开户帮助</div>
                                <div class="card-badge">必备</div>
                            </div>
                            <div class="card-meta">新移民银行开户指南，我懂生活，敢陪你闯</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 10. 我的页面 -->
            <div class="phone-frame">
                <div class="screen">
                    <div class="status-bar" style="color: white;">
                        <span>9:41</span>
                        <span>🔋100%</span>
                    </div>
                    <div class="screen-content profile-screen">
                        <div class="profile-header">
                            <div class="avatar">🦊</div>
                            <div class="username">欢迎回来，我的VIP主角</div>
                            <div class="user-stats">
                                <div>📝 发布 12</div>
                                <div>❤️ 收藏 28</div>
                                <div>👥 关注 156</div>
                            </div>
                        </div>
                        <div class="menu-list">
                            <div class="menu-item">
                                <div>📝 我的发布</div>
                                <div>></div>
                            </div>
                            <div class="menu-item">
                                <div>❤️ 我的收藏</div>
                                <div>></div>
                            </div>
                            <div class="menu-item">
                                <div>📄 我的简历</div>
                                <div>></div>
                            </div>
                            <div class="menu-item">
                                <div>🔔 消息通知</div>
                                <div>></div>
                            </div>
                            <div class="menu-item">
                                <div>⚙️ 设置</div>
                                <div>></div>
                            </div>
                            <div class="menu-item">
                                <div>📞 联系客服</div>
                                <div>></div>
                            </div>
                            <div class="menu-item">
                                <div style="color: #e74c3c;">🚪 退出登录</div>
                                <div>></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="screens-grid">
            <!-- Row 3: 详情页面 -->
            <!-- 11. 头条新闻页 -->
            <div class="phone-frame">
                <div class="screen">
                    <div class="status-bar">
                        <span>9:41</span>
                        <span>🔋100%</span>
                    </div>
                    <div class="screen-content content-page">
                        <div class="page-title">📰 小机灵日报</div>
                        <div class="feature-card" style="background: linear-gradient(135deg, #fff8f0 0%, #f0f8ff 100%);">
                            <div class="card-header">
                                <div class="card-title">🦊 小机灵说</div>
                                <div class="card-badge">今日推荐</div>
                            </div>
                            <div class="card-meta">"我今天看了100条资讯，这3条值得你知道"</div>
                        </div>
                        <div class="feature-card">
                            <div class="card-header">
                                <div class="card-title">荷兰签证新政策</div>
                                <div class="card-badge">重要</div>
                            </div>
                            <div class="card-meta">📅 2024年新规 | 🔍 影响留学生<br>签证延期流程简化，AI帮你解读重点</div>
                        </div>
                        <div class="feature-card">
                            <div class="card-header">
                                <div class="card-title">阿姆斯特丹房租上涨</div>
                                <div class="card-badge">关注</div>
                            </div>
                            <div class="card-meta">📈 平均涨幅8% | 💡 应对策略<br>房租涨价不可怕，小机灵教你省钱技巧</div>
                        </div>
                        <div class="feature-card">
                            <div class="card-header">
                                <div class="card-title">中秋节活动汇总</div>
                                <div class="card-badge">节日</div>
                            </div>
                            <div class="card-meta">🥮 华人社区活动 | 🎊 商家优惠<br>今天是中秋节，记得给远方的人发个祝福</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 12. 餐厅详情页 -->
            <div class="phone-frame">
                <div class="screen">
                    <div class="status-bar">
                        <span>9:41</span>
                        <span>🔋100%</span>
                    </div>
                    <div class="screen-content content-page">
                        <div style="background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%); color: white; padding: 20px; border-radius: 16px; margin-bottom: 16px; text-align: center; position: relative;">
                            <div style="position: absolute; top: 8px; right: 16px; font-size: 20px; opacity: 0.7;">🌸</div>
                            <div style="font-size: 20px; margin-bottom: 6px;">🍜</div>
                            <div style="font-size: 12px; font-weight: 700;">海底捞火锅</div>
                            <div style="font-size: 8px; opacity: 0.9;">⭐ 4.8 (328条评价) | 小机灵推荐</div>
                        </div>
                        <div class="feature-card">
                            <div class="card-title">📍 地址信息</div>
                            <div class="card-meta">Damrak 123, 1012 Amsterdam<br>地铁2号线直达，出站步行3分钟</div>
                        </div>
                        <div class="feature-card">
                            <div class="card-title">⏰ 营业时间</div>
                            <div class="card-meta">周一至周日 11:00-23:00<br>非高峰时段：14:00-17:00 无需排队</div>
                        </div>
                        <div class="feature-card">
                            <div class="card-title">💰 人均消费</div>
                            <div class="card-meta">€25-30 | 支持微信/支付宝<br>学生证9折，小机灵用户额外95折</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 13. 聊天界面 -->
            <div class="phone-frame">
                <div class="screen">
                    <div class="status-bar">
                        <span>9:41</span>
                        <span>🔋100%</span>
                    </div>
                    <div class="screen-content" style="background: linear-gradient(135deg, #fef9f3 0%, #f0f8ff 100%);">
                        <div style="background: white; padding: 16px; border-bottom: 2px solid #f8f6f0; display: flex; align-items: center; gap: 12px;">
                            <div style="width: 32px; height: 32px; border-radius: 50%; background: linear-gradient(135deg, #FF8A65 0%, #FFB74D 100%); display: flex; align-items: center; justify-content: center; color: white; font-size: 14px;">🦊</div>
                            <div>
                                <div style="font-size: 12px; font-weight: 700; color: #2c3e50;">小机灵AI助手</div>
                                <div style="font-size: 8px; color: #FF8A65;">在线 - 24/7为您服务</div>
                            </div>
                        </div>
                        <div style="padding: 16px; height: calc(100% - 120px); overflow-y: auto;">
                            <div style="background: #e3f2fd; padding: 12px; border-radius: 12px; margin-bottom: 12px; font-size: 9px; max-width: 80%;">
                                您好！我是小机灵，你的专属数字搭子 🦊<br>有什么生活问题需要帮助吗？
                            </div>
                            <div style="background: white; padding: 12px; border-radius: 12px; margin-bottom: 12px; font-size: 9px; margin-left: 20%; border: 1px solid #e8f4f8;">
                                我想了解荷兰的医疗保险，太复杂了
                            </div>
                            <div style="background: #e3f2fd; padding: 12px; border-radius: 12px; margin-bottom: 12px; font-size: 9px; max-width: 80%;">
                                别急，我正在和生活死磕 💪<br><br>荷兰医疗保险分为基础保险和补充保险：<br>• 基础保险：强制性，约€1200-1500/年<br>• 补充保险：可选，看牙科等<br><br>我给你整理成图了，放心看不晕 📊
                            </div>
                        </div>
                        <div style="position: absolute; bottom: 0; left: 0; right: 0; background: white; padding: 12px; border-top: 2px solid #f8f6f0; display: flex; gap: 12px;">
                            <input type="text" style="flex: 1; padding: 8px 12px; border: 2px solid #e8f4f8; border-radius: 20px; font-size: 9px; font-family: 'Noto Sans SC', sans-serif;" placeholder="输入你的问题...">
                            <button style="width: 32px; height: 32px; border-radius: 50%; background: linear-gradient(135deg, #FF8A65 0%, #FFB74D 100%); color: white; border: none; font-size: 9px; font-weight: 600;">发送</button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 14. 设置页面 -->
            <div class="phone-frame">
                <div class="screen">
                    <div class="status-bar">
                        <span>9:41</span>
                        <span>🔋100%</span>
                    </div>
                    <div class="screen-content content-page">
                        <div class="page-title">⚙️ 设置</div>
                        <div class="feature-card">
                            <div class="card-title">🌐 语言设置</div>
                            <div class="card-meta">中文简体 | 支持中英荷三语 ></div>
                        </div>
                        <div class="feature-card">
                            <div class="card-title">🔔 通知设置</div>
                            <div class="card-meta">小机灵推送已开启 | 智能推荐 ></div>
                        </div>
                        <div class="feature-card">
                            <div class="card-title">🔒 隐私设置</div>
                            <div class="card-meta">个人信息保护 | 数据安全 ></div>
                        </div>
                        <div class="feature-card">
                            <div class="card-title">📍 位置服务</div>
                            <div class="card-meta">阿姆斯特丹 | 精准本地推荐 ></div>
                        </div>
                        <div class="feature-card">
                            <div class="card-title">💳 支付设置</div>
                            <div class="card-meta">微信支付、支付宝、iDEAL ></div>
                        </div>
                        <div class="feature-card">
                            <div class="card-title">🦊 小机灵个性化</div>
                            <div class="card-meta">AI助手偏好设置 | 语录推送 ></div>
                        </div>
                        <div class="feature-card">
                            <div class="card-title">ℹ️ 关于小机灵</div>
                            <div class="card-meta">版本 1.0.0 | 生活是江湖，机灵来破局 ></div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 15. 空状态页面 -->
            <div class="phone-frame">
                <div class="screen">
                    <div class="status-bar">
                        <span>9:41</span>
                        <span>🔋100%</span>
                    </div>
                    <div class="screen-content" style="background: linear-gradient(135deg, #fef9f3 0%, #f0f8ff 100%); display: flex; flex-direction: column; align-items: center; justify-content: center; text-align: center; padding: 24px;">
                        <div style="font-size: 48px; margin-bottom: 16px; opacity: 0.6;">🦊</div>
                        <div style="font-size: 14px; font-weight: 700; color: #2c3e50; margin-bottom: 8px;">暂无内容</div>
                        <div style="font-size: 10px; color: #7f8c8d; margin-bottom: 20px; line-height: 1.4;">别急，我正在和生活死磕<br>很快就有好内容推荐给你</div>
                        <div style="background: linear-gradient(135deg, #FF8A65 0%, #FFB74D 100%); color: white; padding: 10px 20px; border-radius: 20px; font-size: 9px; font-weight: 600;">刷新试试</div>
                        <div style="position: absolute; bottom: 40px; font-size: 8px; color: #95a5a6; font-style: italic;">"小机灵不多，我是唯一"</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>

            <!-- 4. Restaurant Page -->
            <div class="phone-frame">
                <div class="screen">
                    <div class="status-bar">
                        <span>9:41</span>
                        <span>🔋100%</span>
                    </div>
                    <div class="screen-content content-page">
                        <div class="page-title">🍜 美食推荐</div>
                        <div class="category-tabs">
                            <div class="tab active">中餐</div>
                            <div class="tab">西餐</div>
                            <div class="tab">日韩</div>
                            <div class="tab">外卖</div>
                        </div>
                        <div class="feature-card">
                            <div class="card-header">
                                <div class="card-title">海底捞火锅</div>
                                <div class="card-badge">热门</div>
                            </div>
                            <div class="card-meta">⭐ 4.8 | 📍 市中心 | 💰 €25/人</div>
                        </div>
                        <div class="feature-card">
                            <div class="card-header">
                                <div class="card-title">兰州拉面</div>
                                <div class="card-badge">新店</div>
                            </div>
                            <div class="card-meta">⭐ 4.6 | 📍 唐人街 | 💰 €12/人</div>
                        </div>
                        <div class="feature-card">
                            <div class="card-header">
                                <div class="card-title">🤖 AI美食助手</div>
                                <div class="card-badge">智能</div>
                            </div>
                            <div class="card-meta">根据口味偏好推荐餐厅</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 5. Housing Page -->
            <div class="phone-frame">
                <div class="screen">
                    <div class="status-bar">
                        <span>9:41</span>
                        <span>🔋100%</span>
                    </div>
                    <div class="screen-content content-page">
                        <div class="page-title">🏠 租房信息</div>
                        <div class="category-tabs">
                            <div class="tab active">整租</div>
                            <div class="tab">合租</div>
                            <div class="tab">学生房</div>
                            <div class="tab">短租</div>
                        </div>
                        <div class="feature-card">
                            <div class="card-header">
                                <div class="card-title">市中心2室1厅</div>
                                <div class="card-badge">新房源</div>
                            </div>
                            <div class="card-meta">💰 €1800/月 | 📍 阿姆斯特丹中心</div>
                        </div>
                        <div class="feature-card">
                            <div class="card-header">
                                <div class="card-title">学生公寓单间</div>
                                <div class="card-badge">包水电</div>
                            </div>
                            <div class="card-meta">💰 €650/月 | 📍 乌特勒支大学区</div>
                        </div>
                        <div class="feature-card">
                            <div class="card-header">
                                <div class="card-title">🤖 AI找房助手</div>
                                <div class="card-badge">智能</div>
                            </div>
                            <div class="card-meta">智能匹配理想房源</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="screens-grid">
            <!-- Row 2: Feature Screens -->
            <!-- 6. Jobs Page -->
            <div class="phone-frame">
                <div class="screen">
                    <div class="status-bar">
                        <span>9:41</span>
                        <span>🔋100%</span>
                    </div>
                    <div class="screen-content content-page">
                        <div class="page-title">💼 求职招聘</div>
                        <div class="category-tabs">
                            <div class="tab active">兼职</div>
                            <div class="tab">全职</div>
                            <div class="tab">实习</div>
                            <div class="tab">远程</div>
                        </div>
                        <div class="feature-card">
                            <div class="card-header">
                                <div class="card-title">中文客服</div>
                                <div class="card-badge">急招</div>
                            </div>
                            <div class="card-meta">💰 €15/小时 | 📍 阿姆斯特丹</div>
                        </div>
                        <div class="feature-card">
                            <div class="card-header">
                                <div class="card-title">软件工程师</div>
                                <div class="card-badge">高薪</div>
                            </div>
                            <div class="card-meta">💰 €4500/月 | 📍 海牙</div>
                        </div>
                        <div class="feature-card">
                            <div class="card-header">
                                <div class="card-title">🤖 AI简历优化</div>
                                <div class="card-badge">免费</div>
                            </div>
                            <div class="card-meta">AI帮你优化简历内容</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 7. Travel Page -->
            <div class="phone-frame">
                <div class="screen">
                    <div class="status-bar">
                        <span>9:41</span>
                        <span>🔋100%</span>
                    </div>
                    <div class="screen-content content-page">
                        <div class="page-title">✈️ 旅行规划</div>
                        <div class="category-tabs">
                            <div class="tab active">欧洲游</div>
                            <div class="tab">周边游</div>
                            <div class="tab">一日游</div>
                            <div class="tab">攻略</div>
                        </div>
                        <div class="feature-card">
                            <div class="card-header">
                                <div class="card-title">巴黎3日游</div>
                                <div class="card-badge">热门</div>
                            </div>
                            <div class="card-meta">🚄 高铁直达 | 💰 €299起</div>
                        </div>
                        <div class="feature-card">
                            <div class="card-header">
                                <div class="card-title">羊角村一日游</div>
                                <div class="card-badge">推荐</div>
                            </div>
                            <div class="card-meta">🚌 大巴接送 | 💰 €45/人</div>
                        </div>
                        <div class="feature-card">
                            <div class="card-header">
                                <div class="card-title">🤖 AI行程规划</div>
                                <div class="card-badge">智能</div>
                            </div>
                            <div class="card-meta">个性化定制旅行路线</div>
                        </div>
                    </div>
                </div>
            </div>
