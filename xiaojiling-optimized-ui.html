<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>小机灵 - 荷兰华人数字生活平台 (优化版)</title>
    <link href="https://fonts.googleapis.com/css2?family=PingFang+SC:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'PingFang SC', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Microsoft YaHei', sans-serif;
            background: #f5f5f5;
            color: #333;
            padding: 20px;
            min-height: 100vh;
            font-size: 12px;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
        }

        .header {
            text-align: center;
            margin-bottom: 32px;
        }

        .title {
            font-size: 24px;
            font-weight: 600;
            color: #333;
            margin-bottom: 8px;
        }

        .subtitle {
            font-size: 14px;
            color: #666;
            margin-bottom: 16px;
        }

        .screens-grid {
            display: grid;
            grid-template-columns: repeat(5, 1fr);
            gap: 20px;
            margin-bottom: 32px;
        }

        .phone-frame {
            width: 260px;
            height: 520px;
            background: #000;
            border-radius: 20px;
            padding: 6px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.15);
            transition: transform 0.3s ease;
        }

        .phone-frame:hover {
            transform: translateY(-4px);
        }

        .screen {
            width: 100%;
            height: 100%;
            background: #fff;
            border-radius: 16px;
            overflow: hidden;
            position: relative;
        }

        .status-bar {
            height: 20px;
            background: #000;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 12px;
            font-size: 10px;
            color: white;
            font-weight: 500;
        }

        .screen-content {
            height: calc(100% - 20px);
            position: relative;
            overflow: hidden;
        }

        /* 美团风格首页 */
        .meituan-home {
            background: linear-gradient(135deg, #FFD700 0%, #FFA500 100%);
        }

        .location-bar {
            background: linear-gradient(135deg, #FFD700 0%, #FFA500 100%);
            padding: 8px 12px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .location-text {
            font-size: 12px;
            color: #333;
            font-weight: 500;
        }

        .search-section {
            background: linear-gradient(135deg, #FFD700 0%, #FFA500 100%);
            padding: 0 12px 12px;
        }

        .search-box {
            background: white;
            border-radius: 20px;
            padding: 8px 16px;
            display: flex;
            align-items: center;
            gap: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .search-input {
            border: none;
            outline: none;
            flex: 1;
            font-size: 12px;
            color: #666;
        }

        .search-btn {
            background: #FFD700;
            border: none;
            padding: 4px 12px;
            border-radius: 12px;
            font-size: 10px;
            color: #333;
            font-weight: 500;
        }

        .main-content {
            background: white;
            flex: 1;
            padding: 12px;
        }

        .service-grid {
            display: grid;
            grid-template-columns: repeat(5, 1fr);
            gap: 12px;
            margin-bottom: 16px;
        }

        .service-item {
            text-align: center;
            padding: 8px 4px;
        }

        .service-icon {
            width: 32px;
            height: 32px;
            border-radius: 8px;
            margin: 0 auto 4px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
        }

        .service-label {
            font-size: 10px;
            color: #333;
            line-height: 1.2;
        }

        .section-title {
            font-size: 14px;
            font-weight: 600;
            color: #333;
            margin: 16px 0 8px;
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .product-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 8px;
            margin-bottom: 12px;
        }

        .product-card {
            background: white;
            border-radius: 8px;
            overflow: hidden;
            border: 1px solid #f0f0f0;
        }

        .product-image {
            width: 100%;
            height: 60px;
            background: #f5f5f5;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
        }

        .product-info {
            padding: 6px 8px;
        }

        .product-title {
            font-size: 11px;
            color: #333;
            font-weight: 500;
            margin-bottom: 2px;
            line-height: 1.3;
        }

        .product-price {
            font-size: 12px;
            color: #ff6b35;
            font-weight: 600;
        }

        .product-meta {
            font-size: 9px;
            color: #999;
            margin-top: 2px;
        }

        /* 底部导航 */
        .bottom-nav {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 50px;
            background: white;
            border-top: 1px solid #f0f0f0;
            display: flex;
            justify-content: space-around;
            align-items: center;
        }

        .nav-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            font-size: 9px;
            color: #999;
            position: relative;
        }

        .nav-item.active {
            color: #FFD700;
        }

        .nav-item.center {
            background: #FFD700;
            width: 36px;
            height: 36px;
            border-radius: 18px;
            color: white;
            font-size: 18px;
            margin-top: -8px;
        }

        .nav-icon {
            font-size: 16px;
            margin-bottom: 2px;
        }

        .nav-badge {
            position: absolute;
            top: -2px;
            right: -2px;
            background: #ff4757;
            color: white;
            border-radius: 6px;
            padding: 1px 4px;
            font-size: 8px;
            min-width: 12px;
            text-align: center;
        }

        /* 今日头条风格新闻页 */
        .toutiao-news {
            background: #fff;
        }

        .news-header {
            background: #ff4757;
            padding: 8px 12px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .news-search {
            flex: 1;
            background: rgba(255,255,255,0.2);
            border-radius: 16px;
            padding: 6px 12px;
            font-size: 11px;
            color: white;
            border: none;
            outline: none;
        }

        .news-search::placeholder {
            color: rgba(255,255,255,0.7);
        }

        .news-tabs {
            background: white;
            padding: 8px 12px;
            display: flex;
            gap: 16px;
            border-bottom: 1px solid #f0f0f0;
        }

        .news-tab {
            font-size: 12px;
            color: #666;
            padding: 4px 0;
            position: relative;
        }

        .news-tab.active {
            color: #ff4757;
            font-weight: 500;
        }

        .news-tab.active::after {
            content: '';
            position: absolute;
            bottom: -8px;
            left: 50%;
            transform: translateX(-50%);
            width: 16px;
            height: 2px;
            background: #ff4757;
            border-radius: 1px;
        }

        .news-list {
            padding: 8px 12px;
            height: calc(100% - 120px);
            overflow-y: auto;
        }

        .news-item {
            display: flex;
            gap: 8px;
            padding: 8px 0;
            border-bottom: 1px solid #f5f5f5;
        }

        .news-content {
            flex: 1;
        }

        .news-title {
            font-size: 12px;
            color: #333;
            line-height: 1.4;
            margin-bottom: 4px;
            font-weight: 500;
        }

        .news-meta {
            font-size: 9px;
            color: #999;
            display: flex;
            gap: 8px;
        }

        .news-image {
            width: 60px;
            height: 45px;
            background: #f5f5f5;
            border-radius: 4px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
        }

        /* 小红书风格智能页 */
        .xiaohongshu-style {
            background: #fff;
        }

        .xhs-header {
            background: white;
            padding: 8px 12px;
            border-bottom: 1px solid #f0f0f0;
        }

        .xhs-search {
            background: #f5f5f5;
            border-radius: 16px;
            padding: 6px 12px;
            font-size: 11px;
            color: #666;
            border: none;
            outline: none;
            width: 100%;
        }

        .xhs-content {
            padding: 8px;
            height: calc(100% - 80px);
            overflow-y: auto;
        }

        .xhs-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 6px;
        }

        .xhs-card {
            background: white;
            border-radius: 8px;
            overflow: hidden;
            margin-bottom: 8px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }

        .xhs-image {
            width: 100%;
            height: 80px;
            background: #f5f5f5;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
        }

        .xhs-info {
            padding: 6px 8px;
        }

        .xhs-title {
            font-size: 10px;
            color: #333;
            line-height: 1.3;
            margin-bottom: 4px;
            font-weight: 500;
        }

        .xhs-author {
            font-size: 8px;
            color: #999;
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .xhs-avatar {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #ddd;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1 class="title">小机灵 - 荷兰华人数字生活平台</h1>
            <p class="subtitle">参考美团、今日头条、小红书等成熟App设计风格</p>
        </div>
        
        <div class="screens-grid">
            <!-- 美团风格首页 -->
            <div class="phone-frame">
                <div class="screen">
                    <div class="status-bar">
                        <span>15:10</span>
                        <span>🔋100%</span>
                    </div>
                    <div class="screen-content meituan-home">
                        <div class="location-bar">
                            <span>📍</span>
                            <span class="location-text">阿姆斯特丹</span>
                            <span style="margin-left: auto; font-size: 10px;">></span>
                        </div>
                        
                        <div class="search-section">
                            <div class="search-box">
                                <span>🔍</span>
                                <input type="text" class="search-input" placeholder="搜索餐厅、活动、服务">
                                <button class="search-btn">搜索</button>
                            </div>
                        </div>
                        
                        <div class="main-content">
                            <div class="service-grid">
                                <div class="service-item">
                                    <div class="service-icon" style="background: #ff6b35;">📰</div>
                                    <div class="service-label">闻</div>
                                </div>
                                <div class="service-item">
                                    <div class="service-icon" style="background: #4ecdc4;">🍽️</div>
                                    <div class="service-label">吃</div>
                                </div>
                                <div class="service-item">
                                    <div class="service-icon" style="background: #45b7d1;">🎭</div>
                                    <div class="service-label">乐</div>
                                </div>
                                <div class="service-item">
                                    <div class="service-icon" style="background: #f39c12;">🤖</div>
                                    <div class="service-label">智</div>
                                </div>
                                <div class="service-item">
                                    <div class="service-icon" style="background: #e74c3c;">🏠</div>
                                    <div class="service-label">住房</div>
                                </div>
                                <div class="service-item">
                                    <div class="service-icon" style="background: #9b59b6;">💼</div>
                                    <div class="service-label">工作</div>
                                </div>
                                <div class="service-item">
                                    <div class="service-icon" style="background: #2ecc71;">🚗</div>
                                    <div class="service-label">出行</div>
                                </div>
                                <div class="service-item">
                                    <div class="service-icon" style="background: #f1c40f;">💰</div>
                                    <div class="service-label">优惠</div>
                                </div>
                                <div class="service-item">
                                    <div class="service-icon" style="background: #34495e;">🏥</div>
                                    <div class="service-label">医疗</div>
                                </div>
                                <div class="service-item">
                                    <div class="service-icon" style="background: #e67e22;">📚</div>
                                    <div class="service-label">教育</div>
                                </div>
                            </div>
                            
                            <div class="section-title">
                                🔥 热门推荐
                            </div>
                            
                            <div class="product-grid">
                                <div class="product-card">
                                    <div class="product-image">🍜</div>
                                    <div class="product-info">
                                        <div class="product-title">海底捞火锅</div>
                                        <div class="product-price">€25/人</div>
                                        <div class="product-meta">⭐4.8 · 市中心</div>
                                    </div>
                                </div>
                                <div class="product-card">
                                    <div class="product-image">🎨</div>
                                    <div class="product-info">
                                        <div class="product-title">梵高博物馆</div>
                                        <div class="product-price">€19</div>
                                        <div class="product-meta">⭐4.9 · 博物馆区</div>
                                    </div>
                                </div>
                                <div class="product-card">
                                    <div class="product-image">🏠</div>
                                    <div class="product-info">
                                        <div class="product-title">市中心公寓</div>
                                        <div class="product-price">€1800/月</div>
                                        <div class="product-meta">2室1厅 · 阿姆斯特丹</div>
                                    </div>
                                </div>
                                <div class="product-card">
                                    <div class="product-image">💼</div>
                                    <div class="product-info">
                                        <div class="product-title">软件工程师</div>
                                        <div class="product-price">€4500/月</div>
                                        <div class="product-meta">全职 · 海牙</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="bottom-nav">
                            <div class="nav-item active">
                                <div class="nav-icon">🏠</div>
                                <div>首页</div>
                            </div>
                            <div class="nav-item">
                                <div class="nav-icon">💬</div>
                                <div>消息</div>
                                <div class="nav-badge">3</div>
                            </div>
                            <div class="nav-item center">
                                <div>+</div>
                            </div>
                            <div class="nav-item">
                                <div class="nav-icon">🛒</div>
                                <div>购物车</div>
                                <div class="nav-badge">2</div>
                            </div>
                            <div class="nav-item">
                                <div class="nav-icon">👤</div>
                                <div>我的</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 今日头条风格新闻页 -->
            <div class="phone-frame">
                <div class="screen">
                    <div class="status-bar">
                        <span>15:05</span>
                        <span>🔋100%</span>
                    </div>
                    <div class="screen-content toutiao-news">
                        <div class="news-header">
                            <div style="width: 24px; height: 24px; background: white; border-radius: 50%; display: flex; align-items: center; justify-content: center; font-size: 12px;">📰</div>
                            <input type="text" class="news-search" placeholder="搜索荷兰本地新闻">
                            <div style="color: white; font-size: 12px;">+</div>
                            <div style="color: white; font-size: 12px;">AI</div>
                        </div>

                        <div class="news-tabs">
                            <div class="news-tab active">推荐</div>
                            <div class="news-tab">热榜</div>
                            <div class="news-tab">本地</div>
                            <div class="news-tab">签证</div>
                            <div class="news-tab">住房</div>
                            <div class="news-tab">工作</div>
                        </div>

                        <div class="news-list">
                            <div class="news-item">
                                <div class="news-content">
                                    <div class="news-title">荷兰新移民政策出台，技术移民门槛降低</div>
                                    <div class="news-meta">
                                        <span>荷兰华人网</span>
                                        <span>2小时前</span>
                                        <span>❤️ 128</span>
                                    </div>
                                </div>
                                <div class="news-image">🏛️</div>
                            </div>

                            <div class="news-item">
                                <div class="news-content">
                                    <div class="news-title">阿姆斯特丹房租上涨3.2%，学生住房更加紧张</div>
                                    <div class="news-meta">
                                        <span>房产观察</span>
                                        <span>4小时前</span>
                                        <span>❤️ 89</span>
                                    </div>
                                </div>
                                <div class="news-image">🏠</div>
                            </div>

                            <div class="news-item">
                                <div class="news-content">
                                    <div class="news-title">春节庆祝活动即将开始，阿姆斯特丹唐人街准备就绪</div>
                                    <div class="news-meta">
                                        <span>文化生活</span>
                                        <span>6小时前</span>
                                        <span>❤️ 156</span>
                                    </div>
                                </div>
                                <div class="news-image">🎊</div>
                            </div>

                            <div class="news-item">
                                <div class="news-content">
                                    <div class="news-title">荷兰IT行业薪资报告：软件工程师平均年薪增长8%</div>
                                    <div class="news-meta">
                                        <span>职场资讯</span>
                                        <span>8小时前</span>
                                        <span>❤️ 203</span>
                                    </div>
                                </div>
                                <div class="news-image">💻</div>
                            </div>

                            <div class="news-item">
                                <div class="news-content">
                                    <div class="news-title">荷兰健康保险新规定，留学生必须了解的变化</div>
                                    <div class="news-meta">
                                        <span>保险指南</span>
                                        <span>1天前</span>
                                        <span>❤️ 67</span>
                                    </div>
                                </div>
                                <div class="news-image">🏥</div>
                            </div>
                        </div>

                        <div class="bottom-nav">
                            <div class="nav-item">
                                <div class="nav-icon">🏠</div>
                                <div>首页</div>
                            </div>
                            <div class="nav-item">
                                <div class="nav-icon">💬</div>
                                <div>消息</div>
                            </div>
                            <div class="nav-item center">
                                <div>+</div>
                            </div>
                            <div class="nav-item">
                                <div class="nav-icon">🛒</div>
                                <div>购物车</div>
                            </div>
                            <div class="nav-item">
                                <div class="nav-icon">👤</div>
                                <div>我的</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 美团风格餐饮页 -->
            <div class="phone-frame">
                <div class="screen">
                    <div class="status-bar">
                        <span>15:10</span>
                        <span>🔋100%</span>
                    </div>
                    <div class="screen-content">
                        <div class="location-bar">
                            <span>📍</span>
                            <span class="location-text">阿姆斯特丹 · 市中心</span>
                            <span style="margin-left: auto; font-size: 10px;">筛选</span>
                        </div>

                        <div class="search-section">
                            <div class="search-box">
                                <span>🔍</span>
                                <input type="text" class="search-input" placeholder="搜索餐厅、菜系">
                                <button class="search-btn">搜索</button>
                            </div>
                        </div>

                        <div style="background: white; padding: 8px 12px; border-bottom: 1px solid #f0f0f0;">
                            <div style="display: flex; gap: 12px; font-size: 11px;">
                                <span style="color: #ff6b35; font-weight: 500;">综合排序</span>
                                <span style="color: #666;">距离最近</span>
                                <span style="color: #666;">评分最高</span>
                                <span style="color: #666;">人均价格</span>
                            </div>
                        </div>

                        <div style="padding: 8px 12px; background: white; height: calc(100% - 140px); overflow-y: auto;">
                            <div style="border: 1px solid #f0f0f0; border-radius: 8px; margin-bottom: 8px; overflow: hidden;">
                                <div style="display: flex; gap: 8px; padding: 8px;">
                                    <div style="width: 60px; height: 60px; background: #f5f5f5; border-radius: 6px; display: flex; align-items: center; justify-content: center; font-size: 20px;">🍜</div>
                                    <div style="flex: 1;">
                                        <div style="font-size: 12px; font-weight: 500; color: #333; margin-bottom: 2px;">海底捞火锅(阿姆斯特丹店)</div>
                                        <div style="font-size: 9px; color: #999; margin-bottom: 2px;">⭐ 4.8分 · 火锅 · 人均€25</div>
                                        <div style="font-size: 9px; color: #666;">📍 距离500m · Nieuwmarkt 12</div>
                                        <div style="font-size: 9px; color: #ff6b35; margin-top: 2px;">💰 新用户立减€5</div>
                                    </div>
                                </div>
                            </div>

                            <div style="border: 1px solid #f0f0f0; border-radius: 8px; margin-bottom: 8px; overflow: hidden;">
                                <div style="display: flex; gap: 8px; padding: 8px;">
                                    <div style="width: 60px; height: 60px; background: #f5f5f5; border-radius: 6px; display: flex; align-items: center; justify-content: center; font-size: 20px;">🍱</div>
                                    <div style="flex: 1;">
                                        <div style="font-size: 12px; font-weight: 500; color: #333; margin-bottom: 2px;">樱花日料</div>
                                        <div style="font-size: 9px; color: #999; margin-bottom: 2px;">⭐ 4.6分 · 日料 · 人均€18</div>
                                        <div style="font-size: 9px; color: #666;">📍 距离800m · Damrak 45</div>
                                        <div style="font-size: 9px; color: #ff6b35; margin-top: 2px;">🎁 满€30减€8</div>
                                    </div>
                                </div>
                            </div>

                            <div style="border: 1px solid #f0f0f0; border-radius: 8px; margin-bottom: 8px; overflow: hidden;">
                                <div style="display: flex; gap: 8px; padding: 8px;">
                                    <div style="width: 60px; height: 60px; background: #f5f5f5; border-radius: 6px; display: flex; align-items: center; justify-content: center; font-size: 20px;">🥟</div>
                                    <div style="flex: 1;">
                                        <div style="font-size: 12px; font-weight: 500; color: #333; margin-bottom: 2px;">老北京饺子馆</div>
                                        <div style="font-size: 9px; color: #999; margin-bottom: 2px;">⭐ 4.7分 · 中餐 · 人均€15</div>
                                        <div style="font-size: 9px; color: #666;">📍 距离1.2km · Chinatown</div>
                                        <div style="font-size: 9px; color: #ff6b35; margin-top: 2px;">🔥 本周热销</div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="bottom-nav">
                            <div class="nav-item">
                                <div class="nav-icon">🏠</div>
                                <div>首页</div>
                            </div>
                            <div class="nav-item">
                                <div class="nav-icon">💬</div>
                                <div>消息</div>
                            </div>
                            <div class="nav-item center">
                                <div>+</div>
                            </div>
                            <div class="nav-item">
                                <div class="nav-icon">🛒</div>
                                <div>购物车</div>
                            </div>
                            <div class="nav-item">
                                <div class="nav-icon">👤</div>
                                <div>我的</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="screens-grid">
            <!-- 美团风格娱乐页 -->
            <div class="phone-frame">
                <div class="screen">
                    <div class="status-bar">
                        <span>15:10</span>
                        <span>🔋100%</span>
                    </div>
                    <div class="screen-content">
                        <div class="location-bar">
                            <span>📍</span>
                            <span class="location-text">阿姆斯特丹 · 娱乐活动</span>
                            <span style="margin-left: auto; font-size: 10px;">筛选</span>
                        </div>

                        <div class="search-section">
                            <div class="search-box">
                                <span>🔍</span>
                                <input type="text" class="search-input" placeholder="搜索活动、景点、演出">
                                <button class="search-btn">搜索</button>
                            </div>
                        </div>

                        <div style="background: white; padding: 8px 12px; border-bottom: 1px solid #f0f0f0;">
                            <div style="display: flex; gap: 12px; font-size: 11px;">
                                <span style="color: #ff6b35; font-weight: 500;">推荐</span>
                                <span style="color: #666;">本周末</span>
                                <span style="color: #666;">博物馆</span>
                                <span style="color: #666;">演出</span>
                                <span style="color: #666;">旅游</span>
                            </div>
                        </div>

                        <div style="padding: 8px 12px; background: white; height: calc(100% - 140px); overflow-y: auto;">
                            <div style="border: 1px solid #f0f0f0; border-radius: 8px; margin-bottom: 8px; overflow: hidden;">
                                <div style="display: flex; gap: 8px; padding: 8px;">
                                    <div style="width: 60px; height: 60px; background: #f5f5f5; border-radius: 6px; display: flex; align-items: center; justify-content: center; font-size: 20px;">🌷</div>
                                    <div style="flex: 1;">
                                        <div style="font-size: 12px; font-weight: 500; color: #333; margin-bottom: 2px;">库肯霍夫花园门票</div>
                                        <div style="font-size: 9px; color: #999; margin-bottom: 2px;">⭐ 4.9分 · 春季限定 · €19.5</div>
                                        <div style="font-size: 9px; color: #666;">📍 利瑟 · 3月21日-5月12日</div>
                                        <div style="font-size: 9px; color: #ff6b35; margin-top: 2px;">🎫 提前预订9折</div>
                                    </div>
                                </div>
                            </div>

                            <div style="border: 1px solid #f0f0f0; border-radius: 8px; margin-bottom: 8px; overflow: hidden;">
                                <div style="display: flex; gap: 8px; padding: 8px;">
                                    <div style="width: 60px; height: 60px; background: #f5f5f5; border-radius: 6px; display: flex; align-items: center; justify-content: center; font-size: 20px;">🎨</div>
                                    <div style="flex: 1;">
                                        <div style="font-size: 12px; font-weight: 500; color: #333; margin-bottom: 2px;">梵高博物馆夜场</div>
                                        <div style="font-size: 9px; color: #999; margin-bottom: 2px;">⭐ 4.8分 · 艺术展览 · €22</div>
                                        <div style="font-size: 9px; color: #666;">📍 博物馆区 · 每周五18:00-22:00</div>
                                        <div style="font-size: 9px; color: #ff6b35; margin-top: 2px;">🎓 学生票€15</div>
                                    </div>
                                </div>
                            </div>

                            <div style="border: 1px solid #f0f0f0; border-radius: 8px; margin-bottom: 8px; overflow: hidden;">
                                <div style="display: flex; gap: 8px; padding: 8px;">
                                    <div style="width: 60px; height: 60px; background: #f5f5f5; border-radius: 6px; display: flex; align-items: center; justify-content: center; font-size: 20px;">🚢</div>
                                    <div style="flex: 1;">
                                        <div style="font-size: 12px; font-weight: 500; color: #333; margin-bottom: 2px;">运河游船观光</div>
                                        <div style="font-size: 9px; color: #999; margin-bottom: 2px;">⭐ 4.7分 · 观光游览 · €16</div>
                                        <div style="font-size: 9px; color: #666;">📍 中央火车站码头 · 每日10:00-18:00</div>
                                        <div style="font-size: 9px; color: #ff6b35; margin-top: 2px;">🌅 日落时段最美</div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="bottom-nav">
                            <div class="nav-item">
                                <div class="nav-icon">🏠</div>
                                <div>首页</div>
                            </div>
                            <div class="nav-item">
                                <div class="nav-icon">💬</div>
                                <div>消息</div>
                            </div>
                            <div class="nav-item center">
                                <div>+</div>
                            </div>
                            <div class="nav-item">
                                <div class="nav-icon">🛒</div>
                                <div>购物车</div>
                            </div>
                            <div class="nav-item">
                                <div class="nav-icon">👤</div>
                                <div>我的</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 小红书风格智能页 -->
            <div class="phone-frame">
                <div class="screen">
                    <div class="status-bar">
                        <span>15:10</span>
                        <span>🔋100%</span>
                    </div>
                    <div class="screen-content xiaohongshu-style">
                        <div class="xhs-header">
                            <input type="text" class="xhs-search" placeholder="搜索生活攻略、经验分享">
                        </div>

                        <div class="xhs-content">
                            <div class="xhs-grid">
                                <div class="xhs-card">
                                    <div class="xhs-image">🏠</div>
                                    <div class="xhs-info">
                                        <div class="xhs-title">阿姆斯特丹租房攻略｜新手必看避坑指南</div>
                                        <div class="xhs-author">
                                            <div class="xhs-avatar"></div>
                                            <span>荷兰小李</span>
                                        </div>
                                    </div>
                                </div>

                                <div class="xhs-card">
                                    <div class="xhs-image">💼</div>
                                    <div class="xhs-info">
                                        <div class="xhs-title">荷兰找工作经验分享｜从简历到面试全攻略</div>
                                        <div class="xhs-author">
                                            <div class="xhs-avatar"></div>
                                            <span>职场达人</span>
                                        </div>
                                    </div>
                                </div>

                                <div class="xhs-card">
                                    <div class="xhs-image">🍜</div>
                                    <div class="xhs-info">
                                        <div class="xhs-title">阿姆斯特丹中餐厅测评｜哪家最正宗？</div>
                                        <div class="xhs-author">
                                            <div class="xhs-avatar"></div>
                                            <span>美食探店</span>
                                        </div>
                                    </div>
                                </div>

                                <div class="xhs-card">
                                    <div class="xhs-image">🚗</div>
                                    <div class="xhs-info">
                                        <div class="xhs-title">荷兰考驾照全流程｜理论+实践详细攻略</div>
                                        <div class="xhs-author">
                                            <div class="xhs-avatar"></div>
                                            <span>驾考通</span>
                                        </div>
                                    </div>
                                </div>

                                <div class="xhs-card">
                                    <div class="xhs-image">🏥</div>
                                    <div class="xhs-info">
                                        <div class="xhs-title">荷兰医疗保险选择指南｜省钱又实用</div>
                                        <div class="xhs-author">
                                            <div class="xhs-avatar"></div>
                                            <span>保险专家</span>
                                        </div>
                                    </div>
                                </div>

                                <div class="xhs-card">
                                    <div class="xhs-image">🎓</div>
                                    <div class="xhs-info">
                                        <div class="xhs-title">荷兰留学生活指南｜从入学到毕业</div>
                                        <div class="xhs-author">
                                            <div class="xhs-avatar"></div>
                                            <span>留学前辈</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="bottom-nav">
                            <div class="nav-item">
                                <div class="nav-icon">🏠</div>
                                <div>首页</div>
                            </div>
                            <div class="nav-item">
                                <div class="nav-icon">💬</div>
                                <div>消息</div>
                            </div>
                            <div class="nav-item center">
                                <div>+</div>
                            </div>
                            <div class="nav-item">
                                <div class="nav-icon">🛒</div>
                                <div>购物车</div>
                            </div>
                            <div class="nav-item">
                                <div class="nav-icon">👤</div>
                                <div>我的</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
