<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>XiaoJiLing - Dutch Chinese Digital Life Platform MVP</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #ffffff;
            color: #1f2937;
            padding: 32px;
            min-height: 100vh;
            line-height: 1.5;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
        }

        .header {
            text-align: center;
            margin-bottom: 48px;
        }

        .title {
            font-size: 32px;
            font-weight: 700;
            color: #111827;
            margin-bottom: 12px;
            letter-spacing: -0.025em;
        }

        .subtitle {
            font-size: 18px;
            color: #6b7280;
            font-weight: 400;
            margin-bottom: 8px;
        }

        .mvp-note {
            font-size: 14px;
            color: #9ca3af;
            background: #f9fafb;
            padding: 12px 24px;
            border-radius: 8px;
            display: inline-block;
            border-left: 4px solid #3b82f6;
        }

        .screens-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 32px;
            margin-bottom: 48px;
        }

        .phone-frame {
            width: 280px;
            height: 560px;
            background: #f8fafc;
            border-radius: 24px;
            padding: 12px;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            transition: all 0.3s ease;
            border: 1px solid #e5e7eb;
        }

        .phone-frame:hover {
            transform: translateY(-4px);
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        }

        .screen {
            width: 100%;
            height: 100%;
            background: #ffffff;
            border-radius: 20px;
            overflow: hidden;
            position: relative;
            border: 1px solid #f3f4f6;
        }

        .status-bar {
            height: 28px;
            background: #111827;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 16px;
            font-size: 12px;
            color: white;
            font-weight: 500;
        }

        .screen-content {
            height: calc(100% - 28px);
            position: relative;
        }

        /* Welcome Screen */
        .welcome-screen {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            text-align: center;
            color: white;
            padding: 40px 24px;
        }

        .logo {
            font-size: 28px;
            font-weight: 700;
            margin-bottom: 12px;
            letter-spacing: -0.025em;
        }

        .welcome-subtitle {
            font-size: 14px;
            margin-bottom: 40px;
            opacity: 0.9;
            font-weight: 400;
        }

        .lang-options {
            display: flex;
            gap: 12px;
        }

        .lang-btn {
            padding: 8px 16px;
            background: rgba(255,255,255,0.2);
            border: 1px solid rgba(255,255,255,0.3);
            border-radius: 8px;
            color: white;
            font-size: 12px;
            font-weight: 500;
            transition: all 0.2s ease;
            cursor: pointer;
        }

        .lang-btn.active {
            background: rgba(255,255,255,0.3);
            border-color: rgba(255,255,255,0.5);
        }

        /* Home Screen */
        .home-screen {
            background: #ffffff;
            padding: 24px;
        }

        .home-header {
            margin-bottom: 32px;
        }

        .greeting {
            font-size: 24px;
            font-weight: 600;
            color: #111827;
            margin-bottom: 8px;
        }

        .location {
            font-size: 14px;
            color: #6b7280;
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .search-bar {
            background: #f9fafb;
            border: 1px solid #e5e7eb;
            border-radius: 12px;
            padding: 12px 16px;
            margin-bottom: 32px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .search-input {
            border: none;
            background: none;
            flex: 1;
            font-size: 14px;
            color: #374151;
            outline: none;
        }

        .search-input::placeholder {
            color: #9ca3af;
        }

        .modules-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 16px;
        }

        .module-card {
            background: #ffffff;
            border: 1px solid #e5e7eb;
            border-radius: 16px;
            padding: 20px;
            text-align: center;
            transition: all 0.2s ease;
            cursor: pointer;
        }

        .module-card:hover {
            border-color: #3b82f6;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }

        .module-icon {
            font-size: 32px;
            margin-bottom: 12px;
            display: block;
        }

        .module-title {
            font-size: 16px;
            font-weight: 600;
            color: #111827;
            margin-bottom: 4px;
        }

        .module-subtitle {
            font-size: 12px;
            color: #6b7280;
        }

        /* Content Pages */
        .content-page {
            background: #ffffff;
            padding: 24px;
        }

        .page-header {
            margin-bottom: 24px;
        }

        .page-title {
            font-size: 20px;
            font-weight: 600;
            color: #111827;
            margin-bottom: 8px;
        }

        .page-subtitle {
            font-size: 14px;
            color: #6b7280;
        }

        .content-card {
            background: #ffffff;
            border: 1px solid #e5e7eb;
            border-radius: 12px;
            padding: 16px;
            margin-bottom: 16px;
            transition: all 0.2s ease;
        }

        .content-card:hover {
            border-color: #d1d5db;
            box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
        }

        .card-title {
            font-size: 16px;
            font-weight: 600;
            color: #111827;
            margin-bottom: 8px;
        }

        .card-meta {
            font-size: 14px;
            color: #6b7280;
            line-height: 1.5;
        }

        .card-badge {
            background: #3b82f6;
            color: white;
            padding: 4px 8px;
            border-radius: 6px;
            font-size: 12px;
            font-weight: 500;
            display: inline-block;
            margin-bottom: 8px;
        }

        .bottom-nav {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 64px;
            background: #ffffff;
            border-top: 1px solid #e5e7eb;
            display: flex;
            justify-content: space-around;
            align-items: center;
        }

        .nav-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            font-size: 10px;
            color: #9ca3af;
            transition: color 0.2s ease;
        }

        .nav-item.active {
            color: #3b82f6;
        }

        .nav-icon {
            font-size: 20px;
            margin-bottom: 4px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1 class="title">XiaoJiLing</h1>
            <p class="subtitle">Dutch Chinese Digital Life Platform</p>
            <div class="mvp-note">MVP Version - Core Modules: News, Food, Entertainment, Finance</div>
        </div>
        
        <div class="screens-grid">
            <!-- Welcome Screen -->
            <div class="phone-frame">
                <div class="screen">
                    <div class="status-bar">
                        <span>9:41</span>
                        <span>100%</span>
                    </div>
                    <div class="screen-content welcome-screen">
                        <div class="logo">XiaoJiLing</div>
                        <div class="welcome-subtitle">Your Digital Life Companion in Netherlands</div>
                        <div class="lang-options">
                            <div class="lang-btn active">中文</div>
                            <div class="lang-btn">EN</div>
                            <div class="lang-btn">NL</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Home Screen -->
            <div class="phone-frame">
                <div class="screen">
                    <div class="status-bar">
                        <span>9:41</span>
                        <span>100%</span>
                    </div>
                    <div class="screen-content home-screen">
                        <div class="home-header">
                            <div class="greeting">Good morning</div>
                            <div class="location">📍 Amsterdam, Netherlands</div>
                        </div>
                        
                        <div class="search-bar">
                            <span>🔍</span>
                            <input type="text" class="search-input" placeholder="Search services...">
                        </div>
                        
                        <div class="modules-grid">
                            <div class="module-card">
                                <span class="module-icon">📰</span>
                                <div class="module-title">News</div>
                                <div class="module-subtitle">Local updates</div>
                            </div>
                            <div class="module-card">
                                <span class="module-icon">🍽️</span>
                                <div class="module-title">Food</div>
                                <div class="module-subtitle">Restaurants & dining</div>
                            </div>
                            <div class="module-card">
                                <span class="module-icon">🎭</span>
                                <div class="module-title">Entertainment</div>
                                <div class="module-subtitle">Events & activities</div>
                            </div>
                            <div class="module-card">
                                <span class="module-icon">💰</span>
                                <div class="module-title">Finance</div>
                                <div class="module-subtitle">Deals & savings</div>
                            </div>
                        </div>
                        
                        <div class="bottom-nav">
                            <div class="nav-item active">
                                <span class="nav-icon">🏠</span>
                                <span>Home</span>
                            </div>
                            <div class="nav-item">
                                <span class="nav-icon">📰</span>
                                <span>News</span>
                            </div>
                            <div class="nav-item">
                                <span class="nav-icon">🍽️</span>
                                <span>Food</span>
                            </div>
                            <div class="nav-item">
                                <span class="nav-icon">💰</span>
                                <span>Finance</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- News Screen -->
            <div class="phone-frame">
                <div class="screen">
                    <div class="status-bar">
                        <span>9:41</span>
                        <span>100%</span>
                    </div>
                    <div class="screen-content content-page">
                        <div class="page-header">
                            <div class="page-title">Latest News</div>
                            <div class="page-subtitle">Stay updated with local information</div>
                        </div>

                        <div class="content-card">
                            <div class="card-badge">Breaking</div>
                            <div class="card-title">New Visa Policy Updates</div>
                            <div class="card-meta">Important changes for international students and workers in Netherlands. Updated application procedures and requirements.</div>
                        </div>

                        <div class="content-card">
                            <div class="card-badge">Housing</div>
                            <div class="card-title">Amsterdam Rent Market Report</div>
                            <div class="card-meta">Monthly analysis of rental prices and availability in major Dutch cities. Tips for finding affordable housing.</div>
                        </div>

                        <div class="content-card">
                            <div class="card-badge">Community</div>
                            <div class="card-title">Chinese New Year Events</div>
                            <div class="card-meta">Upcoming celebrations and cultural events in Amsterdam, Rotterdam, and The Hague.</div>
                        </div>

                        <div class="bottom-nav">
                            <div class="nav-item">
                                <span class="nav-icon">🏠</span>
                                <span>Home</span>
                            </div>
                            <div class="nav-item active">
                                <span class="nav-icon">📰</span>
                                <span>News</span>
                            </div>
                            <div class="nav-item">
                                <span class="nav-icon">🍽️</span>
                                <span>Food</span>
                            </div>
                            <div class="nav-item">
                                <span class="nav-icon">💰</span>
                                <span>Finance</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Food Screen -->
            <div class="phone-frame">
                <div class="screen">
                    <div class="status-bar">
                        <span>9:41</span>
                        <span>100%</span>
                    </div>
                    <div class="screen-content content-page">
                        <div class="page-header">
                            <div class="page-title">Food & Dining</div>
                            <div class="page-subtitle">Discover restaurants and cuisines</div>
                        </div>

                        <div class="content-card">
                            <div class="card-badge">Recommended</div>
                            <div class="card-title">Golden Dragon Restaurant</div>
                            <div class="card-meta">⭐ 4.8 • Chinese Cuisine • €€€<br>📍 Nieuwmarkt, Amsterdam<br>Authentic Sichuan dishes with modern presentation</div>
                        </div>

                        <div class="content-card">
                            <div class="card-badge">New</div>
                            <div class="card-title">Ramen House Utrecht</div>
                            <div class="card-meta">⭐ 4.6 • Japanese • €€<br>📍 City Center, Utrecht<br>Fresh ramen bowls and authentic Japanese atmosphere</div>
                        </div>

                        <div class="content-card">
                            <div class="card-badge">Popular</div>
                            <div class="card-title">Dim Sum Palace</div>
                            <div class="card-meta">⭐ 4.7 • Cantonese • €€<br>📍 Chinatown, The Hague<br>Traditional dim sum served all day</div>
                        </div>

                        <div class="bottom-nav">
                            <div class="nav-item">
                                <span class="nav-icon">🏠</span>
                                <span>Home</span>
                            </div>
                            <div class="nav-item">
                                <span class="nav-icon">📰</span>
                                <span>News</span>
                            </div>
                            <div class="nav-item active">
                                <span class="nav-icon">🍽️</span>
                                <span>Food</span>
                            </div>
                            <div class="nav-item">
                                <span class="nav-icon">💰</span>
                                <span>Finance</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="screens-grid">
            <!-- Entertainment Screen -->
            <div class="phone-frame">
                <div class="screen">
                    <div class="status-bar">
                        <span>9:41</span>
                        <span>100%</span>
                    </div>
                    <div class="screen-content content-page">
                        <div class="page-header">
                            <div class="page-title">Entertainment</div>
                            <div class="page-subtitle">Events, activities & travel</div>
                        </div>

                        <div class="content-card">
                            <div class="card-badge">This Weekend</div>
                            <div class="card-title">Keukenhof Gardens Tour</div>
                            <div class="card-meta">🌷 Spring flower exhibition<br>📅 March 21 - May 12<br>🚌 Group tour with Chinese guide available</div>
                        </div>

                        <div class="content-card">
                            <div class="card-badge">Cultural</div>
                            <div class="card-title">Van Gogh Museum Night</div>
                            <div class="card-meta">🎨 Special evening exhibition<br>📅 Every Friday 6-10 PM<br>🎫 Student discounts available</div>
                        </div>

                        <div class="content-card">
                            <div class="card-badge">Travel</div>
                            <div class="card-title">Paris Weekend Trip</div>
                            <div class="card-meta">🚄 High-speed train package<br>💰 From €199 per person<br>🏨 Hotel and transport included</div>
                        </div>

                        <div class="bottom-nav">
                            <div class="nav-item">
                                <span class="nav-icon">🏠</span>
                                <span>Home</span>
                            </div>
                            <div class="nav-item">
                                <span class="nav-icon">📰</span>
                                <span>News</span>
                            </div>
                            <div class="nav-item">
                                <span class="nav-icon">🍽️</span>
                                <span>Food</span>
                            </div>
                            <div class="nav-item active">
                                <span class="nav-icon">🎭</span>
                                <span>Entertainment</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Finance Screen -->
            <div class="phone-frame">
                <div class="screen">
                    <div class="status-bar">
                        <span>9:41</span>
                        <span>100%</span>
                    </div>
                    <div class="screen-content content-page">
                        <div class="page-header">
                            <div class="page-title">Finance & Deals</div>
                            <div class="page-subtitle">Save money with exclusive offers</div>
                        </div>

                        <div class="content-card">
                            <div class="card-badge">Limited Time</div>
                            <div class="card-title">Albert Heijn Student Discount</div>
                            <div class="card-meta">🛒 15% off on Asian food products<br>📅 Valid until end of month<br>💳 Show student ID at checkout</div>
                        </div>

                        <div class="content-card">
                            <div class="card-badge">Banking</div>
                            <div class="card-title">ING Bank Account Opening</div>
                            <div class="card-meta">🏦 Free account setup for students<br>💰 No monthly fees for first year<br>📱 English customer support available</div>
                        </div>

                        <div class="content-card">
                            <div class="card-badge">Insurance</div>
                            <div class="card-title">Health Insurance Comparison</div>
                            <div class="card-meta">🏥 Compare plans and prices<br>📋 Simplified application process<br>🗣️ Chinese-speaking advisors</div>
                        </div>

                        <div class="bottom-nav">
                            <div class="nav-item">
                                <span class="nav-icon">🏠</span>
                                <span>Home</span>
                            </div>
                            <div class="nav-item">
                                <span class="nav-icon">📰</span>
                                <span>News</span>
                            </div>
                            <div class="nav-item">
                                <span class="nav-icon">🍽️</span>
                                <span>Food</span>
                            </div>
                            <div class="nav-item active">
                                <span class="nav-icon">💰</span>
                                <span>Finance</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
